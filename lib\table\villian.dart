import 'dart:math' as Math;

import 'package:flutter/material.dart';

class VillianComponent extends StatelessWidget {
  final dynamic player;
  final String seatPosition;
  final Widget amountOnTable;
  static const double _turnRingWidth = 5.5;
  final AnimationController turnTimerController;
  final bool isCurrentTurn;
  final String lastMove;
  final dynamic mySeat;
  final int numPlayers;
  final int seatIndex;
  const VillianComponent(
      {super.key,
      required this.player,
      required String this.seatPosition,
      required Widget this.amountOnTable,
      required AnimationController this.turnTimerController,
      required bool this.isCurrentTurn, required this.mySeat, required int this.seatIndex, required String this.lastMove, required int this.numPlayers});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 150,
      height: 167,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: [
          // Cards image overlayed at the bottom
          if (isCurrentTurn)
            Positioned(
                top: 0 - 2.5,
                //bottom: 50,
                left: 0 - 2.5,
                child: Transform.rotate(
                  angle: -Math.pi * 0.75,
                  child: CircularProgressIndicator(
                      value: (1.0 - turnTimerController.value) * .75,
                      strokeWidth: _turnRingWidth,
                      color: Colors.green,
                      backgroundColor: Colors.transparent,
                      constraints: BoxConstraints.tight(const Size(145, 145))),
                )),
                     Positioned(
                       top: 90,
              right: 73,
                    child:           Transform.rotate(angle: -Math.pi/9,
                child:
                _buildCardBack(),),),
            Positioned(
              top: 90,
                  left:67,
                  child:       Transform.rotate(angle: Math.pi/9,
                child:
                _buildCardBack(),),          
                ),
          // Player image as background
           Image.asset(
              'assets/images/PlayerFrame.png',
              width: 150,
              //height: 150,
            ),
          
          Positioned(
              top: 125,
              child: Stack(
                children: [
                                      Text(
                                        player.name,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w900,
                                          foreground: Paint()
                                            ..style = PaintingStyle.stroke
                                            ..strokeWidth = 4
                                            ..color = Colors.black,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      Text(
                                        player.name,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w900,
                                          color: Colors.white,
                                        ),
                                        textAlign: TextAlign.center,
                                      )

                ]
              )),
          Positioned(
              top: 125,
              left: seatPosition == 'D' ? 145 : 127,
              child: Stack(
                children: [
                                      Text(
                                        seatPosition,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w900,
                                          foreground: Paint()
                                            ..style = PaintingStyle.stroke
                                            ..strokeWidth = 4
                                            ..color = Colors.black,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      Text(
                                        seatPosition,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w900,
                                          color: Colors.white,
                                        ),
                                        textAlign: TextAlign.center,
                                      )

                ]
              )),
          Positioned(
              top: 125,
              left: 155,
              child: Stack(
                children: [
                                      Text(
                                        numPlayers.toString(),
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w900,
                                          foreground: Paint()
                                            ..style = PaintingStyle.stroke
                                            ..strokeWidth = 4
                                            ..color = Colors.black,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      Text(
                                        numPlayers.toString(),
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w900,
                                          color: Colors.white,
                                        ),
                                        textAlign: TextAlign.center,
                                      )

                ]
              )),              
          if (player.contributedThisStreet > 0)
            Positioned(
              top:chip_pos[((  (seatIndex > mySeat) ? seatIndex - mySeat : (9-( mySeat - seatIndex)))%9 )]?['top'] as double,
              left:chip_pos[((  (seatIndex > mySeat) ? seatIndex - mySeat : (9-( mySeat - seatIndex)))%9 )]?['left'] as double,
              child: amountOnTable,
            ), // You can add name/action text here if needed

           Positioned(
            top: 35,
            left: 105,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 226, 226, 226),
                borderRadius: BorderRadius.circular(50),
                border: Border.all(
                    color: const Color.fromARGB(255, 0, 0, 0).withOpacity(0.2)),
              ),
              child: Text(
                '\$${player.chips}',
                style: const TextStyle(
                  color: Color.fromARGB(255, 0, 0, 0),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
                
               Align(
                alignment: Alignment.bottomCenter,
                child:  Stack( children: [
                          Text(
                            lastMove,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w900,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 4
                                ..color = Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            lastMove,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          )
                        ]),
               
               )
         // Text(' ${((  (seatIndex > mySeat) ? seatIndex - mySeat : (9-( mySeat - seatIndex)))%9 ).toString()  }    ${(( seatIndex) ).toString()  }     ${  (( mySeat) ).toString()}', style: TextStyle(color: Colors.blue, fontSize: 12),),    
        ],
      )          
        //  Text(mySeat.toString(), style: TextStyle(color: Colors.red, fontSize: 12),),
        // Text(' ${((  (seatIndex > mySeat) ? seatIndex - mySeat : (9-( mySeat - seatIndex)))%9 ).toString()  }    ${(( seatIndex) ).toString()  }     ${  (( mySeat) ).toString()}', style: TextStyle(color: Colors.blue, fontSize: 12),),    
     
      
    );
  }
  Widget _buildCardBack({double? width, double? height}) {
    // Use scaled dimensions if not provided
    final cardWidth = 40;
    final cardHeight = 60;

    // Scale the border radius and padding based on card size
    final borderRadius = 10;
    final borderPadding = (cardWidth * 0.25).clamp(1.0, 6.0);

    return  Container(
//margin: EdgeInsets.all(2),
          width: cardWidth as double,
          height: cardHeight as double,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4 ),
            border: Border.all(color: Colors.black, width: .00001),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
              padding: EdgeInsets.all(
                  borderPadding * .5), // Creates the white border
              child: Container(
                width: cardWidth - 2 * (borderPadding * 1.2),
                height: cardHeight - 2 * (borderPadding * 1.2),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 41, 151, 214),
                  borderRadius:
                      BorderRadius.circular(4 ),
                ),
                child: Padding(
                  padding: EdgeInsets.all(
                      borderPadding * 1.3 / 4), // Creates the white border
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4 ),
                    child: Container(
                      width: cardWidth - 2 * (borderPadding * 1.8),
                      height: cardHeight - 2 * (borderPadding * 1.3),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 255, 255, 255),
                        borderRadius:
                            BorderRadius.circular(4 ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(0), // Creates the white border
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(4 ),
                          child: Image.asset(
                            'assets/images/Sky.png',
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // Image.asset(
                    //   'assets/images/Sky.png',
                    //   fit: BoxFit.cover,
                    // ),
                  ),
                ),
              )),
        );
  }

}
var chip_pos = {
0:{  'top':220,
  'left':125 - 16,
},

1:{  'top':-33,
  'left':170,
},
2:{  'top':100,
  'left': 250,
},

3:{  'top':150,
  'left':200,
},
4:{  'top':190,
  'left':105,
},

5:{  'top':190,
  'left':105,
},
6:{  'top':140,
  'left':10,
},

7:{  'top':60,
  'left':-15,
},
8:{  'top': -33,
  'left':50,
},

};
